# Documentation Rules & Standards

This document defines the unified standards for module documentation in the TEM application. All module `README.md` files should follow these guidelines to ensure clarity, consistency, thoroughness, and ease of onboarding for new developers.

## Core principles

### Review the code first - this is the most important rule

**Before writing or updating any module documentation:**

- **Thoroughly review the actual source code** of the module and its dependencies
- List the module's files and examine key classes/functions
- Verify against the current implementation, not existing documentation
- **Never document components, functions, or features that don't exist in the code**
- **Do not make assumptions** about how the code works without verifying
- **Always check the actual code** before documenting any component or configuration option
- If you're unsure about something, investigate the code further or note it as a question
- It's better to omit information than to include incorrect or assumed information
- Ensure documentation accurately reflects the code's structure, purpose, and usage

### Prioritize clarity and usefulness

The primary goal of module documentation is to be **clear, accurate, and genuinely useful** to developers:

- While consistency is important, **never sacrifice clarity for rigid adherence to a template**
- Adapt the structure and content as needed to best explain the module's purpose
- Focus on what developers need to know to use the module effectively

## Document structure

Every module README should include these elements, organized in a logical flow:

### Module introduction

- Use a main heading with the module name in sentence case (`# Module name`)
- Write a concise introduction that explains:
  - The module's role and main responsibilities within the application
  - Core external libraries or frameworks it builds upon
  - The overall structure of the module (if it has distinct parts)

### Features section

Include a "Features" section (`## Features`) with links to relevant sections:

- Use a bulleted list with links to specific sections (e.g., `[Publish events](#publishing-events)`)
- Focus on concrete actions users can take with the module
- Use actual code references (like `@Decorator()`) to make items recognizable
- Ensure all links point to actual section headings in the document
- This helps users quickly navigate to the functionality they need

### Main content sections

Organize the main content into logical sections based on the module's functionality:

- Use clear, task-oriented headings (e.g., `## Class schemas`, `## Validation`) rather than generic component-based headings
- **Prefer a task-oriented approach** that organizes documentation around what users want to accomplish
- **Group related functionality** under appropriate headings when the module contains distinct functional areas
- Use sentence case for all headings (e.g., "Creating ZIP archives" not "Creating ZIP Archives")

Structure each section with:

- A clear heading describing the functionality
- A brief introduction explaining the purpose and context
- Code examples with explanatory text before and after
- Expected outputs in comments to make functionality immediately clear

### Configuration section

If the module is configurable, include a dedicated Configuration section (`## Configuration`):

- Place this section at a logical point in the document - either after the introduction (if configuration is needed before using the module) or after the main content (if it's supplementary information)
- Provide a complete example configuration object with inline comments explaining each option
- Format file references in comments consistently and concisely (e.g., "// config/filename.ts" or "// filename.ts (purpose)")
- Include default values directly in the example where applicable
- **Always verify configuration options against actual code files** like `jobs-config.ts` and `config/default.ts` to avoid documenting non-existent options
- Only document actual application-level configuration options, not internal implementation details
- **Integrate configuration options descriptions** into the examples rather than as separated lists to be more concise and show options in context
- Keep the configuration example concise but comprehensive
- Include information about environment variables or other configuration methods if applicable

### API endpoints section

If the module includes an API controller, create a dedicated top-level section (`## API endpoints`):

- Document REST APIs in a standard API documentation style using a table format with columns for:
  - Method (GET, POST, PUT, DELETE)
  - Endpoint
  - Description
  - Request Body
  - Response
- Document request parameters, both path and query parameters
- Document request body schema for POST/PUT requests
- Document response format and status codes (use "-" for empty responses like 204 No Content)
- Include example responses in JSON format for endpoints that return data
- Group related endpoints under logical sections (e.g., "User Management")
- Include authentication requirements if applicable
- **Document the API contract directly:** Show the endpoint, parameters, and responses rather than client code examples (like httpClient.get())

### Implementation details section

Include a dedicated section (`## Implementation details`) at the end of the document to provide deeper technical insights for developers who want to understand the module's architecture:

- Place this section at the very end of the document, after all other sections
- Focus on information that's relevant to developers using the module, not internal implementation details
- Include information about dependencies, limitations, and performance considerations
- Explain any non-obvious behavior or edge cases
- Keep it concise and focused on what developers need to know
- **Structure the components section** into logical categories with clear headings:
  - Use subsections like `### Core components`, `### Utilities`, and `### UI components`
  - Bold the component names for better scannability (e.g., **[`ComponentName`]**)
  - Provide brief descriptions of what each component does
- **Include links to source files** for all components (e.g., `[ComponentName](./component-file.ts)`)
- **Structure implementation details** into logical subsections with clear headings:
  - Use "### Architecture details" for structural and design aspects
  - Use specific subsections for integration points (e.g., "### HTTP integration")
  - Group related settings under "### Default settings"
- **Avoid redundancy** by focusing on details not already covered in earlier sections
- **Highlight architectural decisions** rather than repeating basic functionality
- Use **bold keywords** at the start of each point to make scanning easier

## Content guidelines

### Writing style

- **Be accurate:** Ensure documentation reflects the current state of the code
- **Be clear:** Use straightforward language; avoid jargon or explain it when necessary
- **Be specific:** Provide concrete examples rather than abstract descriptions
- **Focus on the "why" and "how":** Explain why the module exists and how to use it
- **Document the present:** Focus only on current implementation; don't mention deprecated features
- **Incorporate important information** directly in relevant sections rather than collecting notes at the end
- **Focus only on content that's useful to the reader:** Do not include meta-documentation sections like "Documentation note" or explanations about the documentation itself
- **For modules that contain both backend code and frontend components**, place the "User Interface" section near the end of the documentation, before the Implementation details section
- **Use precise terminology for interface components:** Refer to "application interface" or "dedicated section in the application interface" rather than "web UI"

### Formatting

#### Markdown formatting

Use standard Markdown formatting for readability and consistency across all documentation.

- **Use appropriate heading levels**
  - H1 (`#`) for module name
  - H2 (`##`) for main sections
  - H3 (`###`) for subsections
- **Use lists appropriately**
  - Bulleted lists for collections of related items
  - Numbered lists only for sequential steps or prioritized items
- **Format code and emphasis**
  - Use code blocks with appropriate syntax highlighting
  - Use bold for emphasis of important terms
  - Use italics sparingly for secondary emphasis

#### Links and references

Always link to relevant resources to provide context and make documentation more navigable.

- **Link to external resources**
  - Link to third-party packages with GitHub, npm, or homepage URLs: `[package-name](https://github.com/org/package-name)`
  - Use descriptive link text that indicates what the link points to
- **Link to internal resources**
  - Link to source files when mentioning specific components: `[Component](./component.ts)`
  - Link to documentation sections when referencing other parts of the document: `[Configuration section](#configuration)`

#### Code comments

Keep comments concise and focused on explaining the 'why' not the 'what'. Comments should add value beyond what's evident from the code itself.

- **Use lowercase for inline comments** (`// comment`)
  - Start all inline comments with lowercase: `// create a new user`, not `// Create a new user`
  - Use sentence fragments without ending punctuation: `// get user by id`, not `// Get the user by ID.`
  - Examples:
    - Good: `// retry on network failure`
    - Good: `// handle edge case when user has no roles`
    - Poor: `// Set the user variable` (obvious from code)
    - Poor: `// This function gets a user` (redundant)
  - Exceptions for capitalization:
    - Acronyms: HTTP, URL, API, JWT
    - Product names: Express, OAuth2, TypeScript
    - Class/interface names: UserService, HttpClient
    - Other proper nouns: Azure, Novartis
- **Use standard capitalization for block comments and JSDoc** (`/* Comment */` or `/** JSDoc */`)

#### File references

Format file references consistently in all comments and text to maintain clarity and readability.

- **Use concise path formats**
  - Use relative paths without leading slashes: `config/default.ts` or `src/common/identity/user.ts`
  - For files with a specific purpose, use parentheses: `config/default.ts (environment configuration)`
- **Keep references simple**
  - Avoid verbose phrases like "in the file" or "located at"
  - Good: `// config/default.ts`
  - Poor: `// in config/default.ts file`

### Code examples

#### General example guidelines

- **Include practical examples** that demonstrate real-world usage scenarios
- **Integrate descriptions with examples** to create an engaging and readable flow
- **Use progressive disclosure** by starting with basic usage and then introducing advanced features
- **Focus on API usage, not implementation details**
- **Keep examples simple and focused** on the relevant functionality
- **Remove irrelevant logic** that distracts from the core functionality
- **Exclude unrelated components** from examples
- Include necessary imports and context for examples to be easily understood
- Do not include trivial examples that bring no value

#### Example formatting

- **Match example style to component type:**
  - For functions: show direct usage without wrapping in classes or constructors
  - For classes/providers: demonstrate usage in a class context
- **Ensure examples match the described functionality**
- **Show expected outputs in comments** to make functionality immediately clear
- **Keep examples focused and minimal:** Only include code that demonstrates the functionality, avoiding assignments to unused variables
- **Show results in comments:** Use comment-based output examples rather than unnecessary console.log calls
- **Place explanatory paragraphs about behavior** after code examples when they describe what happens after the code is executed, to better reflect the sequence of events
- **Use plain text for simple information:** State trivial information (like "The interface is available at /events") in plain text rather than code blocks
- **Explain automatic features in plain text:** For features that require no user code, use explanatory text rather than code examples
- **Make comments add value:** Ensure comments provide new information beyond what's already explained in the surrounding text
- **Place explanatory text outside code blocks:** Put detailed explanations of how code works in regular text, not as comments within code examples
- **Follow logical sequence:** Place explanations about what happens after code execution below the code example, not before it
- **Use appropriate syntax highlighting** for code blocks

#### Special component types

- **For utility functions:**
  - Create simple, focused examples that clearly demonstrate each function's purpose
  - Demonstrate all available utility functions in a single, cohesive example when possible
  - Use a realistic but straightforward context that shows practical application
