# Azure OpenAI module

The Azure OpenAI module provides integration with Azure OpenAI services, enabling access to advanced language models for natural language processing, content generation, and AI-powered features. It serves as the foundation for the [AI module](../../ai/README.md) and provides direct access to Azure OpenAI clients.

## Features

- [Access to Azure OpenAI clients](#accessing-azure-openai-clients) for different models
- [Integration with AI module](#integration-with-ai-module)
- [Configuration](#configuration) management

## Accessing Azure OpenAI clients

The module provides access to Azure OpenAI clients through the `AzureOpenAIManager`:

```typescript
import { AzureOpenAIManager } from '~/common/azure/openai';

@Provider()
export class MyService {
  constructor(private openaiManager: AzureOpenAIManager) {}

  async generateText(prompt: string) {
    const client = this.openaiManager.getClient('gpt-4o-mini');
    const { output_text } = await client.responses.create({
      model: 'gpt-4o-mini',
      input: [{ role: 'user', content: prompt }],
      max_output_tokens: 1000,
      store: false
    });

    return output_text;
  }
}
```

## Integration with AI module

The Azure OpenAI module is primarily used through the [AI module](../../ai/README.md), which provides a higher-level interface:

```typescript
import { AIClient } from '~/common/ai';

@Provider()
export class MyService {
  constructor(private ai: AIClient) {}

  async generateSummary(text: string) {
    return this.ai.getResponse('Summarize the following text in 3 bullet points: ' + text, {
      model: 'gpt-4o-mini'
    });
  }
}
```

## Configuration

The module is configured through the application configuration:

```typescript
// config/default.ts
export default {
  azure: {
    openai: {
      // Azure OpenAI endpoint
      endpoint: 'https://tem.openai.azure.com',

      // API key for authentication
      apiKey: 'azure-openai-api-key'
    }
  }
};
```

The configuration is defined in the `AzureOpenAIConfig` schema:

```typescript
// azure-openai-config.ts
export const AzureOpenAIConfig = config({
  /**
   * Endpoint
   * @example https://ai-palto-tem.openai.azure.com
   */
  endpoint: string(),

  /**
   * API key
   */
  apiKey: string()
});
```

## Implementation details

### Core components

- **[`AzureOpenAIManager`](./azure-openai-manager.ts)**: Manages Azure OpenAI clients for different models
- **[`AzureOpenAIModule`](./azure-openai-module.ts)**: Module definition for dependency injection

### Architecture details

- **Configuration-based setup**: Uses application configuration for endpoint and API key
- **Client management**: Creates and caches Azure OpenAI clients for different models
- **Integration with AI module**: Provides the foundation for the [AI module](../../ai/README.md)'s functionality
- **Dependency injection**: Automatically provided through the module system

### Default settings

- **API version**: Fixed to '2025-03-01-preview' in the client creation
- **Client caching**: Clients are cached per model to avoid recreation
- **Endpoint and API key**: Required configuration parameters from application config
